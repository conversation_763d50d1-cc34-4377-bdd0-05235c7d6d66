#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def main():
    print("=" * 50)
    print("نظام إدارة المدارس - اختبار التشغيل")
    print("School Management System - Test Run")
    print("=" * 50)
    
    print(f"Python Version: {sys.version}")
    print(f"Current Directory: {os.getcwd()}")
    print(f"Script Path: {__file__}")
    
    # فحص الملفات الأساسية
    print("\nChecking required files:")
    files_to_check = [
        'main.py',
        'config/app_config.py', 
        'config/database_config.py',
        'gui/login_window.py',
        'utils/security.py'
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - NOT FOUND")
            all_files_exist = False
    
    if not all_files_exist:
        print("\nSome required files are missing!")
        return False
    
    # فحص المكتبات الأساسية
    print("\nChecking basic libraries:")
    try:
        import tkinter
        print("✓ tkinter")
    except ImportError:
        print("✗ tkinter - NOT AVAILABLE")
        return False
    
    # محاولة استيراد الوحدات الأساسية
    print("\nTesting module imports:")
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from config.app_config import AppConfig
        print("✓ AppConfig imported")
        
        from config.database_config import DatabaseConfig  
        print("✓ DatabaseConfig imported")
        
        from utils.security import SecurityManager
        print("✓ SecurityManager imported")
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All basic checks passed!")
    print("✓ جميع الفحوصات الأساسية نجحت!")
    
    # محاولة تشغيل النظام
    try:
        print("\nAttempting to start the system...")
        print("محاولة تشغيل النظام...")
        
        from main import SchoolManagementApp
        app = SchoolManagementApp()
        
        print("✓ Application object created successfully")
        print("✓ تم إنشاء كائن التطبيق بنجاح")
        
        # لا نشغل الواجهة الرسومية في الاختبار
        print("\nTest completed successfully!")
        print("اكتمل الاختبار بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Error starting system: {e}")
        print(f"✗ خطأ في تشغيل النظام: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 System is ready to run!")
        print("🎉 النظام جاهز للتشغيل!")
        print("\nTo start the system, run:")
        print("لتشغيل النظام، استخدم:")
        print("python main.py")
    else:
        print("❌ System has issues that need to be resolved")
        print("❌ النظام يحتوي على مشاكل تحتاج إلى حل")
    
    input("\nPress Enter to exit...")
    input("اضغط Enter للخروج...")
