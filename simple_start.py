#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

print("=" * 50)
print("نظام إدارة المدارس")
print("School Management System")
print("=" * 50)

print(f"Python Version: {sys.version}")
print(f"Current Directory: {os.getcwd()}")

# فحص الملفات الأساسية
required_files = ['main.py', 'config/app_config.py', 'config/database_config.py']
missing_files = []

for file in required_files:
    if os.path.exists(file):
        print(f"✓ {file} موجود")
    else:
        print(f"✗ {file} مفقود")
        missing_files.append(file)

if missing_files:
    print(f"الملفات المفقودة: {missing_files}")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# محاولة تشغيل النظام
try:
    print("محاولة تشغيل النظام...")
    from main import main
    main()
except Exception as e:
    print(f"خطأ: {e}")
    input("اضغط Enter للخروج...")
