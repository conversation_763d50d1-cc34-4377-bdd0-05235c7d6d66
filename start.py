#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام إدارة المدارس
Simple School Management System Launcher
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🏫 نظام إدارة المدارس - School Management System")
    print("=" * 60)
    print(f"Python Version: {sys.version}")
    print(f"Working Directory: {os.getcwd()}")
    print("=" * 60)

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ خطأ: يتطلب Python 3.6 أو أحدث")
        return False
    print("✅ إصدار Python مناسب")
    return True

def check_tkinter():
    """فحص Tkinter"""
    try:
        import tkinter
        print("✅ Tkinter متوفر")
        return True
    except ImportError:
        print("❌ Tkinter غير متوفر")
        return False

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"تثبيت {package}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print(f"✅ تم تثبيت {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل تثبيت {package}")
        return False

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات"""
    packages = [
        'mysql-connector-python==8.2.0',
        'Pillow==10.1.0', 
        'tkcalendar==1.6.1',
        'reportlab==4.0.7',
        'openpyxl==3.1.2'
    ]
    
    # فحص المكتبات
    missing = []
    
    try:
        import mysql.connector
        print("✅ mysql-connector-python")
    except ImportError:
        missing.append('mysql-connector-python==8.2.0')
    
    try:
        from PIL import Image
        print("✅ Pillow")
    except ImportError:
        missing.append('Pillow==10.1.0')
    
    try:
        import tkcalendar
        print("✅ tkcalendar")
    except ImportError:
        missing.append('tkcalendar==1.6.1')
    
    try:
        import reportlab
        print("✅ reportlab")
    except ImportError:
        missing.append('reportlab==4.0.7')
    
    try:
        import openpyxl
        print("✅ openpyxl")
    except ImportError:
        missing.append('openpyxl==3.1.2')
    
    # تثبيت المكتبات المفقودة
    if missing:
        print(f"\n📦 المكتبات المفقودة: {len(missing)}")
        for package in missing:
            if not install_package(package):
                return False
        print("✅ تم تثبيت جميع المكتبات")
    
    return True

def check_required_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'main.py',
        'config/database_config.py',
        'config/app_config.py'
    ]
    
    missing = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing.append(file_path)
    
    return len(missing) == 0

def run_system():
    """تشغيل النظام الرئيسي"""
    try:
        print("🚀 تشغيل النظام...")
        from main import main as run_main
        run_main()
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد main.py: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def show_setup_dialog():
    """عرض نافذة الإعداد"""
    try:
        root = tk.Tk()
        root.title("إعداد النظام")
        root.geometry("400x300")
        
        tk.Label(root, text="نظام إدارة المدارس", font=('Arial', 16, 'bold')).pack(pady=20)
        
        tk.Label(root, text="يبدو أن النظام يحتاج إلى إعداد", font=('Arial', 12)).pack(pady=10)
        
        def setup_database():
            root.destroy()
            try:
                subprocess.run([sys.executable, 'setup_database.py'])
            except Exception as e:
                print(f"خطأ في تشغيل معالج الإعداد: {e}")
        
        def run_anyway():
            root.destroy()
            run_system()
        
        tk.Button(root, text="إعداد قاعدة البيانات", command=setup_database,
                 bg='blue', fg='white', font=('Arial', 10, 'bold')).pack(pady=10)
        
        tk.Button(root, text="تشغيل النظام", command=run_anyway,
                 bg='green', fg='white', font=('Arial', 10, 'bold')).pack(pady=5)
        
        tk.Button(root, text="إغلاق", command=root.destroy,
                 bg='red', fg='white', font=('Arial', 10, 'bold')).pack(pady=5)
        
        # توسيط النافذة
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في عرض نافذة الإعداد: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # فحص Tkinter
    if not check_tkinter():
        print("يرجى تثبيت tkinter")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    print("\nفحص الملفات المطلوبة...")
    if not check_required_files():
        print("❌ بعض الملفات المطلوبة مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص وتثبيت المكتبات
    print("\nفحص المكتبات...")
    if not check_and_install_requirements():
        print("❌ فشل في تثبيت المكتبات")
        print("يرجى تثبيت المكتبات يدوياً:")
        print("pip install mysql-connector-python Pillow tkcalendar reportlab openpyxl")
        input("اضغط Enter للخروج...")
        return
    
    # محاولة تشغيل النظام
    print("\n" + "=" * 60)
    if not run_system():
        print("\n⚠️ فشل في تشغيل النظام")
        print("قد تحتاج إلى إعداد قاعدة البيانات أولاً")
        
        try:
            show_setup_dialog()
        except:
            print("لإعداد قاعدة البيانات، شغل: python setup_database.py")
            input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
