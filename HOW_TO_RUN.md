# كيفية تشغيل نظام إدارة المدارس

## ✅ النظام جاهز للتشغيل!

تم اختبار النظام بنجاح وهو جاهز للاستخدام الآن.

## 🚀 طرق التشغيل

### الطريقة الأسهل (Windows):
1. انقر نقراً مزدوجاً على ملف: **`RUN_SYSTEM.bat`**
2. سيتم فتح النظام تلقائياً

### الطريقة البديلة:
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع
3. اكتب: `python main.py`
4. اضغط Enter

### طرق أخرى متاحة:
- `python start.py` - تشغيل مع فحص المتطلبات
- `python run_enhanced.py` - تشغيل مع واجهة رسومية محسنة
- `python quick_start.py` - تشغيل سريع مع مساعدة

## 🔑 بيانات الدخول

عند فتح النظام، استخدم:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📋 الميزات المتوفرة حالياً

✅ **إدارة الطلاب**
- إضافة وتعديل وحذف بيانات الطلاب
- البحث والتصفية
- إدارة الصفوف والأقسام

✅ **إدارة المعلمين**
- إدارة بيانات المعلمين
- تحديد التخصصات والمؤهلات
- ربط المعلمين بالصفوف

✅ **نظام الأمان**
- تسجيل دخول آمن
- مستويات صلاحيات مختلفة
- حماية البيانات

✅ **واجهة سهلة الاستخدام**
- تصميم عربي
- واجهات بديهية
- تنقل سهل

## ⚙️ إعداد قاعدة البيانات (إذا لزم الأمر)

إذا واجهت مشكلة في قاعدة البيانات:

1. **تشغيل معالج الإعداد**:
   ```
   python setup_database.py
   ```

2. **أو تعديل الإعدادات يدوياً**:
   - افتح ملف: `config/database_config.py`
   - عدل كلمة مرور MySQL:
   ```python
   'password': 'كلمة_مرور_MySQL_الخاصة_بك'
   ```

## 🔧 حل المشاكل

### مشكلة: "Python is not recognized"
**الحل**: تأكد من تثبيت Python وإضافته إلى PATH

### مشكلة: "Can't connect to MySQL"
**الحل**: 
1. تأكد من تشغيل MySQL Server
2. تحقق من كلمة المرور في `config/database_config.py`

### مشكلة: "Module not found"
**الحل**: ثبت المكتبات المطلوبة:
```
pip install mysql-connector-python Pillow tkcalendar reportlab openpyxl
```

## 📚 ملفات مهمة

- `main.py` - الملف الرئيسي للنظام
- `RUN_SYSTEM.bat` - ملف التشغيل السريع
- `config/database_config.py` - إعدادات قاعدة البيانات
- `USER_GUIDE.md` - دليل المستخدم الشامل
- `INSTALLATION_GUIDE.md` - دليل التثبيت المفصل

## 🎯 الخطوات التالية

بعد تشغيل النظام:
1. سجل دخول بحساب المدير
2. غير كلمة المرور الافتراضية
3. ابدأ بإضافة بيانات الطلاب والمعلمين
4. استكشف الميزات المختلفة

## 📞 الدعم

إذا احتجت مساعدة:
- راجع `USER_GUIDE.md` للاستخدام
- راجع `INSTALLATION_GUIDE.md` للتثبيت
- تحقق من `QUICK_START.md` للبدء السريع

---

**ملاحظة**: النظام تم اختباره وهو جاهز للاستخدام. جميع الملفات والإعدادات في مكانها الصحيح.
