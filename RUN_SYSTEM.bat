@echo off
title School Management System - نظام إدارة المدارس

echo.
echo ===============================================
echo    School Management System
echo    نظام إدارة المدارس
echo ===============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo خطأ: لم يتم العثور على Python!
    echo.
    echo Please install Python from: https://www.python.org/downloads/
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo Python is available
echo Python متوفر
echo.

REM Run the system
echo Starting School Management System...
echo تشغيل نظام إدارة المدارس...
echo.

python main.py

echo.
echo System closed.
echo تم إغلاق النظام.
pause
