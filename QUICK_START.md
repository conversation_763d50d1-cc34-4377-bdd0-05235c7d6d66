# التشغيل السريع - نظام إدارة المدارس

## المتطلبات الأساسية

### 1. Python
- تأكد من تثبيت Python 3.6 أو أحدث
- تحميل من: https://www.python.org/downloads/
- **مهم**: أضف Python إلى PATH أثناء التثبيت

### 2. MySQL Server
- تحميل وتثبيت MySQL Server
- تحميل من: https://dev.mysql.com/downloads/mysql/
- احفظ كلمة مرور المستخدم root

## خطوات التشغيل السريع

### الطريقة 1: استخدام ملف Windows Batch
```
انقر نقراً مزدوجاً على: start.bat
```

### الطريقة 2: استخدام Python
```bash
python start.py
```

### الطريقة 3: التشغيل المباشر
```bash
python main.py
```

## إعداد قاعدة البيانات

### إذا كانت هذه المرة الأولى:
1. شغل معالج إعداد قاعدة البيانات:
   ```bash
   python setup_database.py
   ```

2. أو عدل ملف `config/database_config.py`:
   ```python
   DB_CONFIG = {
       'host': 'localhost',
       'database': 'school_management',
       'user': 'root',
       'password': 'كلمة_مرور_MySQL',  # ضع كلمة مرورك هنا
       'charset': 'utf8mb4'
   }
   ```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## حل المشاكل الشائعة

### مشكلة: "Python is not recognized"
**الحل**: 
- أعد تثبيت Python وتأكد من إضافته إلى PATH
- أو استخدم `py` بدلاً من `python`

### مشكلة: "No module named 'mysql'"
**الحل**:
```bash
pip install mysql-connector-python
```

### مشكلة: "Access denied for user 'root'"
**الحل**:
1. تأكد من كلمة مرور MySQL في `config/database_config.py`
2. أو أنشئ مستخدم جديد في MySQL:
```sql
CREATE USER 'school_admin'@'localhost' IDENTIFIED BY 'password123';
GRANT ALL PRIVILEGES ON *.* TO 'school_admin'@'localhost';
FLUSH PRIVILEGES;
```

### مشكلة: "Can't connect to MySQL server"
**الحل**:
- تأكد من تشغيل MySQL Server
- تحقق من إعدادات الاتصال في `config/database_config.py`

## الملفات المهمة

- `main.py` - الملف الرئيسي
- `start.py` - ملف التشغيل المحسن
- `start.bat` - ملف تشغيل Windows
- `setup_database.py` - معالج إعداد قاعدة البيانات
- `config/database_config.py` - إعدادات قاعدة البيانات

## الدعم

إذا واجهت مشاكل:
1. تأكد من تثبيت Python و MySQL بشكل صحيح
2. راجع ملف `INSTALLATION_GUIDE.md` للتفاصيل
3. راجع ملف `USER_GUIDE.md` لطريقة الاستخدام

---

**ملاحظة**: هذا النظام مصمم للعمل على Windows بشكل أساسي، ولكن يمكن تشغيله على أنظمة أخرى مع تعديلات بسيطة.
