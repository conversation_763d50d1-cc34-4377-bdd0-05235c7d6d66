@echo off
chcp 65001 >nul
title School Management System - نظام إدارة المدارس

echo ===============================================
echo    School Management System
echo    نظام إدارة المدارس
echo ===============================================
echo.

echo Checking Python installation...
echo جاري فحص تثبيت Python...

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    goto :python_found
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    goto :python_found
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    goto :python_found
)

echo ERROR: Python not found!
echo خطأ: لم يتم العثور على Python!
echo.
echo Please install Python 3.6+ from:
echo يرجى تثبيت Python 3.6+ من:
echo https://www.python.org/downloads/
echo.
echo Make sure to check "Add Python to PATH" during installation
echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
pause
exit /b 1

:python_found
echo Python found: %PYTHON_CMD%
echo تم العثور على Python: %PYTHON_CMD%
echo.

echo Checking required libraries...
echo جاري فحص المكتبات المطلوبة...

%PYTHON_CMD% -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: tkinter not available
    echo خطأ: tkinter غير متوفر
    pause
    exit /b 1
)

%PYTHON_CMD% -c "import mysql.connector" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required libraries...
    echo تثبيت المكتبات المطلوبة...
    %PYTHON_CMD% -m pip install mysql-connector-python==8.2.0
    %PYTHON_CMD% -m pip install Pillow==10.1.0
    %PYTHON_CMD% -m pip install tkcalendar==1.6.1
    %PYTHON_CMD% -m pip install reportlab==4.0.7
    %PYTHON_CMD% -m pip install openpyxl==3.1.2
)

echo.
echo Starting the system...
echo جاري تشغيل النظام...
echo.

%PYTHON_CMD% main.py

if %errorlevel% neq 0 (
    echo.
    echo An error occurred while running the system
    echo حدث خطأ أثناء تشغيل النظام
    echo.
    echo Please check:
    echo يرجى التحقق من:
    echo 1. Python is installed correctly
    echo 1. تثبيت Python بشكل صحيح
    echo 2. MySQL Server is installed and running
    echo 2. تثبيت وتشغيل MySQL Server
    echo 3. Database is configured
    echo 3. إعداد قاعدة البيانات
    echo.
    echo To setup database, run:
    echo لإعداد قاعدة البيانات، شغل:
    echo %PYTHON_CMD% setup_database.py
    echo.
)

pause
